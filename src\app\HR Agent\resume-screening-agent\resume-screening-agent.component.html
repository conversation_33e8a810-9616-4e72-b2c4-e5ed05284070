<div class="resume-screening-container">
  <h1>📄 Resume Screening Agent</h1>

  <div class="input-section">
    <label for="jobDescription">Job Description</label>
    <textarea id="jobDescription" [formControl]="jobDescriptionControl" placeholder="Paste job description here..."></textarea>
  </div>

  <div class="input-section">
    <label for="resumeUpload">Upload Resume (PDF)</label>
    <input type="file" id="resumeUpload" (change)="onFileSelected($event)" accept="application/pdf" />
  </div>

  <button (click)="screenResume()">🔍 Analyze Match</button>

  <div class="result" *ngIf="matchResult">
    <h2>Result</h2>
    <p>{{ matchResult }}</p>
  </div>
</div>
