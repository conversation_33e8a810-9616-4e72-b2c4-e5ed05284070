import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { AIAgentInterface, AIKeywordDto, LinkedinMessage } from '../Interface/aiagent-interface';

@Injectable({
  providedIn: 'root'

})
export class ApisService {
  private baseUrl = 'https://localhost:7007/api'  
  
  constructor(private http: HttpClient) {}

  getAIKeywordResponse(): Observable<AIKeywordDto[]> {
    return this.http.get<AIKeywordDto[]>(`${this.baseUrl}/Keywords`);
  }

  getByIdAIKeyword(id: string): Observable<any> {
  return this.http.get(`${this.baseUrl}/Keywords/${id}`);
  }

  postAIAuditEngine(payload: AIAgentInterface): Observable<any> {
    return this.http.post(`${this.baseUrl}/Keywords`, payload);
  }

  deleteAIKeyword(id: number): Observable<boolean> {
  return this.http.delete<boolean>(`${this.baseUrl}/Keywords/${id}`);
  }

  GetLinkedinMessageById(id: string): Observable<LinkedinMessage> {
    return this.http.get<LinkedinMessage>(`${this.baseUrl}/linkedin/messages/${id}`);
  }

  GeneratorLinkedinMessage(url: string): Observable<any>
  {
    return this.http.post(`${this.baseUrl}/linkedin/messages`, { url });
  }

  GeneratorSalesNavigatorMessage(url: string): Observable<any>
  {
    return this.http.post(`${this.baseUrl}/linkedin/sales-navigator/messages`, { url });
  }

  GetAllLinkedinMessage(): Observable<LinkedinMessage[]>
  {
    return this.http.get<LinkedinMessage[]>(`${this.baseUrl}/linkedin/messages`);
  }

  deleteLinkedinMessage(id: number): Observable<boolean> {
    return this.http.delete<boolean>(`${this.baseUrl}/linkedin/messages/${id}`);
  }

}
