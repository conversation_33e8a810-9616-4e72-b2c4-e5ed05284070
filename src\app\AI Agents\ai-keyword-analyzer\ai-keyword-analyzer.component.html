<div class="modal-header">
  <h4 class="modal-title">AI Keyword Analyzer</h4>
  <button type="button" class="close" aria-label="Close" (click)="closeModal()">
    <span aria-hidden="true">&times;</span>
  </button>
</div>

<div class="modal-body">
  <label for="keywordInput">Enter a keyword:</label>
  <input
    type="text"
    id="keywordInput"
    class="form-control mb-3"
    [(ngModel)]="enteredKeyword"
    placeholder="Type a keyword and press Enter"
    (keyup.enter)="submitKeyword()"
  />

  <app-loader [isLoading]="isLoading"></app-loader>

  <div *ngIf="keywordResult">
    <div class="mb-3">
      <h5>Keyword Output</h5>
      <p><strong>Industry:</strong> {{ keywordResult.keywordOutput?.industry }}</p>
      <p><strong>Keywords:</strong> {{ keywordResult.keywordOutput?.keywords?.join(', ') }}</p>
    </div>

    <div class="mb-3">
      <h5>Page Ranking List</h5>
      <ul class="list-unstyled"> <li *ngFor="let item of keywordResult.pagerankingList" class="mb-2">
          <strong>{{ item.position }}. {{ item.source }}</strong>
          <br>
          <a [href]="item.url" target="_blank">{{ item.url }}</a>
          <p class="text-muted small">Keyword: {{ item.keyword }}</p>
        </li>
      </ul>
    </div>

    <div class="mb-3">
      <h5>Processed Keywords</h5>
      <ul class="list-unstyled">
        <li *ngFor="let item of keywordResult.processedKeywords">
          <strong>Keyword:</strong> {{ item.keyword }} <br>
          <span *ngIf="item.aI_Overview"><strong>AI Overview:</strong> {{ item.aI_Overview }}</span><br>
          <span *ngIf="item.paa"><strong>PAA:</strong> {{ item.paa }}</span>
          <span *ngIf="!item.aI_Overview && !item.paa" class="text-muted">No additional details available.</span>
        </li>
      </ul>
    </div>

    <div class="mb-3">
      <h5>AI Analyzer Results</h5>
      <div *ngFor="let item of keywordResult.aiAnalyzerResults" class="card mb-3">
        <div class="card-header">
          <strong>Keyword:</strong> {{ item.keyword }}
          <span *ngIf="item.url"> | <a [href]="item.url" target="_blank">View Source</a></span>
        </div>
        <div class="card-body">
          <div *ngFor="let result of item.aiAnalyzerResponse" class="mb-2">
            <h6>{{ result.section }}</h6>
            <p>{{ result.content }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="modal-footer">
  <button type="button" class="btn btn-primary" (click)="submitKeyword()">Submit</button>
  <button type="button" class="btn btn-secondary" (click)="closeModal()">Close</button>
</div>
