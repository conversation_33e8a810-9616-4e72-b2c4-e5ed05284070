export interface AIAgentInterface {
  keyword: string;
}

export interface AIResponseDto {
  url: string;
  section: string;
  content: string;
}

export interface AIResultItemDto {
  position: number;
  keyword: string;
  url: string;
  source: string;
  aiAnalyzerResponse: AIResponseDto[];
}

export interface AIKeywordDto {
  id:number;
  industry: string;
  aiOverview: string;
  paa: string;
  keywords: string;
  pagesRankings: AIResultItemDto[];
  contentStructures: AIResponseDto[];
}
 
export interface KeywordResponseDto {
  Industry: string;
  Keywords: string[];
}

export interface KeywordAnalysisResult {
  keywordOutput?: {
    industry?: string;
    keywords?: string[];
  };
  pagerankingList?: { position: number; keyword: string; url: string; source: string }[];
  processedKeywords?: { keyword: string; aI_Overview: string | null; paa: string | null }[];
  aiAnalyzerResults?: {
    keyword: string;
    url: string;
    aiAnalyzerResponse: { url: string | null; section: string; content: string }[];
  }[];
}

export interface LinkedinMessage {
  id: number;
  profileName: string;
  profileUrl: string;
  message1?: string;
  message2?: string;
  message3?: string;
  message4?: string;
  message5?: string;
}