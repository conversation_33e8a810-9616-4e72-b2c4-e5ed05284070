<div class="container mt-4">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h2 class="mb-4">Keyword Summary</h2>
    <button class="btn btn-success" (click)="analyzeKeywords()">AI Keyword Analyzer</button>
  </div>

  <app-loader [isLoading]="isLoading"></app-loader>

  <div class="table-responsive" *ngIf="!isLoading && keywords.length > 0">
    <table class="table table-bordered table-striped align-middle">
      <thead class="table-dark">
        <tr>
          <th>Sr. No</th>
          <th>Industry</th>
          <th>AI Overview</th>
          <th>People Also Ask</th>
          <th>Page Rankings</th>
          <th>Content Structures</th>
          <th>Action</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let keyword of keywords; let i = index">
          <td>{{ i + 1 }}</td>
          <td>{{ keyword.industry }}</td>
          <td>
            <ng-container *ngIf="keyword.aiOverview; else noOverview">
              {{ keyword.aiOverview }}
            </ng-container>
            <ng-template #noOverview><em>No overview</em></ng-template>
          </td>
          <td>
            <ng-container *ngIf="keyword.paa; else noPAA">
              <ul class="mb-0 ps-3">
                <li *ngFor="let question of keyword.paa.split(',')">{{ question.trim() }}</li>
              </ul>
            </ng-container>
            <ng-template #noPAA><em>No PAA</em></ng-template>
          </td>
          <td>{{ PagesRankings || 0 }}</td>
          <td>{{ ContentStructures || 0 }}</td>
          <td>
            <button class="btn btn-primary btn-sm me-2" (click)="viewKeyword(keyword.id)">View</button>
            <button class="btn btn-danger btn-sm" (click)="deleteKeyword(keyword.id)">Delete</button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Empty state message -->
  <div *ngIf="!isLoading && (!keywords || keywords.length === 0)" class="text-center text-muted mt-4">
    <p>No keyword data available. Please run the analyzer.</p>
  </div>
</div>
