<div *ngIf="keyword" class="container">
  <h2>{{ keyword.industry }}</h2>

  <section>
    <h3>AI Overview</h3>
    <p>{{ keyword.aiOverview }}</p>
  </section>

  <section>
    <h3>Keywords</h3>
    <p>{{ keyword.keywords }}</p>
  </section>

  <section>
    <h3>People Also Ask</h3>
    <ul>
      <li *ngFor="let question of keyword.paa.split(',')">
        {{ question.trim() }}
      </li>
    </ul>
  </section>
 <section *ngIf="keyword.pagesRankings?.length">
  <h3>Page Rankings</h3>
  <table class="table table-bordered table-sm">
    <thead>
      <tr>
        <th>Position</th>
        <th>URL</th>
        <th>Source</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let rank of keyword.pagesRankings">
        <td>{{ rank.position }}</td>
        <td><a [href]="rank.url" target="_blank">{{ rank.url }}</a></td>
        <td>{{rank.source}}</td>
      </tr>
    </tbody>
  </table>
</section>
  <section *ngIf="keyword.contentStructures?.length">
  <h3>Content Structures</h3>
  <table class="table table-bordered table-sm">
    <thead>
      <tr>
        <th>Url</th>
        <th>Section</th>
        <th>Content</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let item of keyword.contentStructures">
        <td>{{item.url}}</td>
        <td>{{ item.section }}</td>
        <td>{{ item.content }}</td>
      </tr>
    </tbody>
  </table>
</section>


</div>
