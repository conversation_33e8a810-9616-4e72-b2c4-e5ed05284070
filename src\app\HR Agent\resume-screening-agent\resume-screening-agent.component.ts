import { CommonModule, isPlatformBrowser } from '@angular/common';
import { Component, Inject, PLATFORM_ID } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';


@Component({
  selector: 'app-resume-screening-agent',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './resume-screening-agent.component.html',
  styleUrl: './resume-screening-agent.component.scss'
})
export class ResumeScreeningAgentComponent {

  jobDescriptionControl = new FormControl('');
  extractedResumeText = '';
  matchResult = '';
  private pdfjsLib: any = null;

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {
    if (isPlatformBrowser(this.platformId)) {
      this.initializePdfJs();
    }
  }

  private async initializePdfJs() {
    try {
      this.pdfjsLib = await import('pdfjs-dist');
      this.pdfjsLib.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/5.3.31/pdf.worker.min.mjs`;
    } catch (error) {
      console.error('Failed to load PDF.js:', error);
    }
  }

  async onFileSelected(event: Event) {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (!file || file.type !== 'application/pdf') {
      alert('Please upload a valid PDF file.');
      return;
    }

    if (!isPlatformBrowser(this.platformId) || !this.pdfjsLib) {
      alert('PDF processing is not available in this environment.');
      return;
    }

    this.extractedResumeText = await this.extractTextFromPDF(file);
  }

  async extractTextFromPDF(file: File): Promise<string> {
    if (!this.pdfjsLib) {
      throw new Error('PDF.js is not loaded');
    }

    const arrayBuffer = await file.arrayBuffer();
    const pdf = await this.pdfjsLib.getDocument({ data: arrayBuffer }).promise;
    let text = '';

    for (let i = 1; i <= pdf.numPages; i++) {
      const page = await pdf.getPage(i);
      const content = await page.getTextContent();
      const pageText = content.items.map((item: any) => item.str).join(' ');
      text += pageText + '\n';
    }

    return text;
  }

  screenResume() {
    const jobDesc = this.jobDescriptionControl.value || '';
    console.log("Job Description",jobDesc)
    const resume = this.extractedResumeText || '';
    console.log("Resume",resume)
  }
}
