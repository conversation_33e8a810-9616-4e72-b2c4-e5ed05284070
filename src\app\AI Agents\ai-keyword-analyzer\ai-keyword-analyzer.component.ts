import { FormsModule } from '@angular/forms';
import { Component, inject } from '@angular/core';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { AIAgentInterface, KeywordAnalysisResult } from '../../Interface/aiagent-interface';
import { ApisService } from '../../Services/apis.service';
import { CommonModule, NgFor } from '@angular/common';
import { LoaderComponent } from '../../shared/loader/loader.component';

@Component({
  selector: 'app-ai-keyword-analyzer',
  standalone: true,
  imports: [FormsModule, NgFor, CommonModule, LoaderComponent],
  providers: [BsModalService, ApisService],
  templateUrl: './ai-keyword-analyzer.component.html',
  styleUrl: './ai-keyword-analyzer.component.scss'
})
export class AiKeywordAnalyzerComponent {

  keywordResult: KeywordAnalysisResult | undefined;
  enteredKeyword: string = '';
  isLoading: boolean = false;

  private apiService = inject(ApisService);
   
  constructor(public bsModalRef: BsModalRef) {}

   submitKeyword() {
     if (this.isLoading || !this.enteredKeyword.trim()) {
      return;
    }

    const payload: AIAgentInterface = {
      keyword: this.enteredKeyword.trim(),
    };

    this.isLoading = true;
    this.keywordResult = undefined;

    this.apiService.postAIAuditEngine(payload).subscribe({
    next: (res) => {
      console.log('Response:', res);
      this.keywordResult = res;
      this.isLoading = false;
    },
    error: (err) => {
      console.error('API Error:', err);
    }
  });
    console.log('Submitted:', payload);
    //this.closeModal();
  }

  closeModal() {
    this.bsModalRef.hide();
  }
}
