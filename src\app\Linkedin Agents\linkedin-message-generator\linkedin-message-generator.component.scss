.container {
  padding: 20px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 0 12px rgba(0, 0, 0, 0.05);
}

h2 {
  font-weight: 600;
  font-size: 1.8rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.table {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  margin-top: 20px;
}

.table th,
.table td {
  padding: 16px;
  vertical-align: top;
  white-space: normal;
  border: 1px solid #eee;
  font-size: 0.95rem;
}

.table th {
  background-color: #f9f9f9;
  font-weight: 600;
}

a {
  color: #0a66c2;
  font-weight: 600;
  text-decoration: none;
  transition: color 0.3s ease;

  &:hover {
    color: #004182;
    text-decoration: underline;
  }
}

.action-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  justify-content: center;
}

.btn-view {
  background-color: #0dcaf0;
  color: white;
  font-weight: 600;
  border: none;
  padding: 6px 14px;
  border-radius: 6px;
  transition: background-color 0.2s ease-in;

  &:hover {
    background-color: #0bbcd5;
  }
}

.btn-delete {
  background-color: #dc3545;
  color: white;
  font-weight: 600;
  border: none;
  padding: 6px 14px;
  border-radius: 6px;
  transition: background-color 0.2s ease-in;

  &:hover {
    background-color: #bb2d3b;
  }
}
