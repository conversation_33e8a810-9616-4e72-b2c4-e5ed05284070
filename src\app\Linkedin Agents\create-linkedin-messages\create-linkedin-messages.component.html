<div class="container">
  <h1>{{ mode === 1 ? 'LinkedIn Profile Scraper' : 'LinkedIn Sales Navigator Scraper' }}</h1>

  <input [(ngModel)]="url" placeholder="{{ mode === 1 ? 'Enter LinkedIn profile URL' : 'Enter LinkedIn Sales Navigator URL' }}" />
  <button (click)="fetchProfile()" [disabled]="isLoading || !isValidUrl()">Scraping</button>

  <div *ngIf="url && !isValidUrl()" class="error">
    {{ getUrlErrorMessage() }}
    <div *ngIf="mode === 1">
      Example: https://www.linkedin.com/in/sapreet-gujjar-669297211/
    </div>
    <div *ngIf="mode === 2">
      Example: https://www.linkedin.com/sales/search/people?query=... or https://www.linkedin.com/sales/profile/123456789
    </div>
  </div>

  <div *ngIf="isLoading" class="loader">Loading...</div>
  <div *ngIf="error" class="error">{{ error }}</div>

  <div *ngIf="result?.profileName" class="result-box">
    <h2>{{ result.profileName }}</h2>
    <p>
      <a [href]="result.profileUrl" target="_blank" rel="noopener">
        {{ result.profileUrl }}
      </a>
    </p>

    <h3>Generated Messages</h3>
    <div *ngFor="let key of messageKeys()">
      <div class="message">
        <strong>{{ key | titlecase }}:</strong>
        <p>{{ result[key] }}</p>
      </div>
    </div>

    <div class="actions" *ngIf="hasGeneratedMessages()">
      <button (click)="approveMessage()" class="approve-btn">Approve Message</button>
      <button (click)="ScrapeProfileAgain()" class="scrape-again-btn">Scrape Profile Again</button>
    </div>
  </div>
</div>
