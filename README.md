# 🤖 AI Agent SaaS UI

A comprehensive **Angular-based SaaS platform** that provides multiple AI-powered agents for business automation across marketing, sales, and HR domains.

![Angular](https://img.shields.io/badge/Angular-19.2.0-red?style=flat-square&logo=angular)
![TypeScript](https://img.shields.io/badge/TypeScript-5.7.2-blue?style=flat-square&logo=typescript)
![Bootstrap](https://img.shields.io/badge/Bootstrap-5.3.6-purple?style=flat-square&logo=bootstrap)
![License](https://img.shields.io/badge/License-Private-yellow?style=flat-square)

## 🚀 Overview

This platform offers intelligent automation through specialized AI agents that help organizations streamline their business processes. Each agent is designed for specific use cases while maintaining a unified, user-friendly interface.

## ✨ Features

### 🧠 **AI Insights & Analysis**
- **AI Audit Engine**: Comprehensive keyword analysis and SEO insights
- **Keyword Rank Analyzer**: Advanced SEO ranking analysis with competitor research
- **Content Structure Analysis**: AI-powered content optimization recommendations

### 💼 **LinkedIn Automation**
- **LinkedIn Message Generator**: Personalized outreach message creation
- **Sales Navigator Integration**: Advanced LinkedIn prospecting automation
- **Multi-message Templates**: Generate up to 5 different message variations

### 👥 **HR & Recruitment**
- **Resume Screening Agent**: Automated PDF resume analysis
- **Job Description Matching**: AI-powered candidate-job fit scoring
- **Bulk Resume Processing**: Efficient screening of multiple candidates

### 📊 **Analytics & Reporting**
- **Performance Dashboards**: Real-time insights and metrics
- **Export Capabilities**: PDF and data export functionality
- **Historical Data Tracking**: Comprehensive audit trails

## 🛠️ Technology Stack

| Category | Technology |
|----------|------------|
| **Frontend** | Angular 19.2.0, TypeScript 5.7.2 |
| **Styling** | Bootstrap 5.3.6, SCSS, ngx-bootstrap |
| **State Management** | RxJS, Angular Services |
| **PDF Processing** | pdfjs-dist, html2pdf.js |
| **Server** | Express.js (SSR), Angular Universal |
| **Testing** | Karma, Jasmine |
| **Build Tools** | Angular CLI, Webpack |

## 🏗️ Project Structure

```
src/
├── app/
│   ├── AI Agents/           # AI analysis and audit components
│   │   ├── ai-audit-engine/
│   │   ├── ai-keyword-analyzer/
│   │   └── keyword-rank-analyzer/
│   ├── Linkedin Agents/     # LinkedIn automation components
│   │   ├── linkedin-message-generator/
│   │   └── create-linkedin-messages/
│   ├── HR Agent/           # Human resources components
│   │   └── resume-screening-agent/
│   ├── Services/           # API and business logic services
│   ├── Interface/          # TypeScript interfaces and models
│   ├── shared/            # Reusable components and utilities
│   └── sidebar/           # Navigation component
├── assets/                # Static assets
└── styles.scss           # Global styles
```

## 🚀 Quick Start

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn
- Angular CLI (`npm install -g @angular/cli`)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd AI-Agent-SaaS-UI
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   ng serve
   ```

4. **Open your browser**
   Navigate to `http://localhost:4200/`

### Backend Setup

Ensure your backend API is running on `https://localhost:7007/api` or update the base URL in `src/app/Services/apis.service.ts`.

## 🔧 Development

### Available Scripts

| Command | Description |
|---------|-------------|
| `ng serve` | Start development server |
| `ng build` | Build for production |
| `ng test` | Run unit tests |
| `ng lint` | Run linting |
| `ng e2e` | Run end-to-end tests |

### Code Generation

Generate new components:
```bash
ng generate component component-name
ng generate service service-name
ng generate module module-name
```

### Environment Configuration

Update API endpoints in `src/app/Services/apis.service.ts`:
```typescript
private baseUrl = 'https://your-api-domain.com/api'
```

## 📱 Usage Guide

### 1. AI Audit Engine
- Navigate to "🧠 AI Insights"
- Enter keywords for analysis
- View comprehensive SEO insights and recommendations

### 2. LinkedIn Message Generator
- Go to "💬 LinkedIn Message Generator"
- Input LinkedIn profile or Sales Navigator URLs
- Generate personalized outreach messages

### 3. Resume Screening
- Access "📄 Resume Screening Agent"
- Upload PDF resumes
- Enter job descriptions for automated matching

### 4. Keyword Analysis
- Use "📈 Keyword Rank Analyzer"
- Analyze keyword performance and rankings
- Export results for further analysis

## 🔌 API Integration

The application integrates with a RESTful API providing the following endpoints:

```typescript
// Keyword Analysis
GET    /api/Keywords
POST   /api/Keywords
GET    /api/Keywords/{id}
DELETE /api/Keywords/{id}

// LinkedIn Messages
GET    /api/linkedin/messages
POST   /api/linkedin/messages
GET    /api/linkedin/messages/{id}
DELETE /api/linkedin/messages/{id}
POST   /api/linkedin/sales-navigator/messages
```

## 🧪 Testing

### Unit Tests
```bash
ng test
```

### End-to-End Tests
```bash
ng e2e
```

### Test Coverage
```bash
ng test --code-coverage
```

## 📦 Building for Production

### Standard Build
```bash
ng build --configuration production
```

### Server-Side Rendering
```bash
npm run build:ssr
npm run serve:ssr
```

## 🚀 Deployment

### Static Hosting
1. Build the project: `ng build --configuration production`
2. Deploy the `dist/` folder to your hosting provider

### Server-Side Rendering
1. Build for SSR: `npm run build:ssr`
2. Deploy and run: `npm run serve:ssr:AI-Agent-SaaS-UI`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Commit changes: `git commit -am 'Add new feature'`
4. Push to branch: `git push origin feature/new-feature`
5. Submit a pull request

## 📄 License

This project is private and proprietary. All rights reserved.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation wiki

## 🔄 Changelog

### Version 0.0.0 (Current)
- Initial release with core AI agent functionality
- LinkedIn message generation
- Resume screening capabilities
- Keyword analysis tools

---

**Built with ❤️ using Angular and TypeScript**
