import { ChangeDetectorRef, Component, inject, Injector, OnInit } from '@angular/core';
import { NgI<PERSON>, NgFor, CommonModule } from '@angular/common';
import { BsModalService } from 'ngx-bootstrap/modal';
import { <PERSON><PERSON>eywordDto } from '../../Interface/aiagent-interface';
import { ApisService } from '../../Services/apis.service';
import { AiKeywordAnalyzerComponent } from '../ai-keyword-analyzer/ai-keyword-analyzer.component';
import { LoaderComponent } from '../../shared/loader/loader.component';
import { ViewAiKeywordAnalyzerComponent } from '../view-ai-keyword-analyzer/view-ai-keyword-analyzer.component';

@Component({
  selector: 'app-ai-audit-engine',
  standalone: true,
  imports: [NgIf, NgFor, CommonModule, LoaderComponent],
  providers: [BsModalService],
  templateUrl: './ai-audit-engine.component.html',
  styleUrl: './ai-audit-engine.component.scss'
})
export class AiAuditEngineComponent implements OnInit {

  keywords: AIKeywordDto[] = [];
  PagesRankings: any;
  ContentStructures: any;
  isLoading: boolean = false;

  private _modalService = inject(BsModalService);
  private apiService = inject(ApisService);
  constructor(
    private injector: Injector,
    protected _cdr: ChangeDetectorRef
  ) {}

   ngOnInit(): void {
    this.GetAll()
  }


 GetAll() {
  this.isLoading = true;
  this.apiService.getAIKeywordResponse().subscribe({
    next: (data) => {
      this.keywords = data;
      this.PagesRankings = data[0].pagesRankings.length;
      this.ContentStructures = data[0].contentStructures.length;
      this.isLoading = false;
    },
    error: (err) => {
      console.error('Error fetching keywords:', err);
      this.keywords = [];
      this.isLoading = false;
    }
  });
}

  analyzeKeywords()
  {
    try {
          const Dialog = this._modalService.show(AiKeywordAnalyzerComponent, {
            class: 'modal-lg',
          });
        } catch (error) {
          console.error('Error opening create lead modal:', error);
        }
  }

  viewKeyword(payload:any)
  {
    console.log("View ID",payload)
      try {
    const dialogRef = this._modalService.show(ViewAiKeywordAnalyzerComponent, {
      class: 'modal-lg',
     initialState: { id: payload }
    });
  } catch (error) {
    console.error('Error opening create lead modal:', error);
  }
  }

  deleteKeyword(id:number)
  {
    console.log("deleteKeyword ID",id)
    this.apiService.deleteAIKeyword(id).subscribe({
      next: () => {
      console.log('Keyword deleted successfully');
      this.GetAll(); 
    },
    error: (err) => {
    console.error('Delete failed', err);
    }
    });
  }
}
