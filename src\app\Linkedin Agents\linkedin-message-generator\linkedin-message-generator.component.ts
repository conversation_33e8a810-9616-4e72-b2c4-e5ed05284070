import { <PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import { BsModalService } from 'ngx-bootstrap/modal';
import { CreateLinkedinMessagesComponent } from '../create-linkedin-messages/create-linkedin-messages.component';
import { ApisService } from '../../Services/apis.service';
import { LinkedinMessage } from '../../Interface/aiagent-interface';

@Component({
  selector: 'app-linkedin-message-generator',
  standalone: true,
  imports: [NgFor,NgIf],
  providers: [BsModalService],
  templateUrl: './linkedin-message-generator.component.html',
  styleUrl: './linkedin-message-generator.component.scss'
})
export class LinkedinMessageGeneratorComponent implements OnInit {
 
  users: LinkedinMessage[] = [];
  private _modalService = inject(BsModalService);
  private apiService = inject(ApisService);

  ngOnInit(): void {
    this.GetAll();
  }

  GetAll()
  {
    this.apiService.GetAllLinkedinMessage().subscribe({
      next: (response) => {
        this.users = response;
      },
      error: (err) => {
        console.error('API Error:', err);
      }
    });
  }
  
 createNewMessage(mode: number) {
  try {
    this._modalService.show(CreateLinkedinMessagesComponent, {
      class: 'modal-lg',
      initialState: { mode: mode }
    });
  } catch (error) {
    console.error('Error opening create message modal:', error);
  }
}

 viewUser(user: number) {
   alert(user)
   this.apiService.GetLinkedinMessageById(user.toString()).subscribe({
    next: (response) => {
      console.log("Response", response)
    },
    error: (err) => {
      console.error('API Error:', err);
    }
  });
 }

 deleteUser(user: any) {
  if (confirm(`Are you sure you want to delete messages for ${user.profileName}?`)) {
    this.apiService.deleteLinkedinMessage(user.id).subscribe({
      next: () => {
        this.GetAll();
      },
      error: (err) => {
        console.error('Delete failed', err);
      }
    });
  }
 }

 handleHeaderAction(actionType: number): void {
  switch (actionType) {
    case 1:
      this.createNewMessage(1);
      break;
    case 2:
      this.createNewMessage(2);
      break;
    default:
      console.warn('Unknown action type:', actionType);
  }
}


}
