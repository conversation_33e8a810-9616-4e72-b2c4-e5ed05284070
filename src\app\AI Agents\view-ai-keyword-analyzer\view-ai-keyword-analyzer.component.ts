import { ChangeDetectorRef, Component, inject, Injector, OnInit } from '@angular/core';
import { BsModalService } from 'ngx-bootstrap/modal';
import { ApisService } from '../../Services/apis.service';
import { CommonModule, NgFor, NgIf } from '@angular/common';
import { LoaderComponent } from '../../shared/loader/loader.component';
import { AIKeywordDto } from '../../Interface/aiagent-interface';

@Component({
  selector: 'app-view-ai-keyword-analyzer',
  standalone: true,
  imports: [NgIf, NgFor, CommonModule],
  providers: [BsModalService, ApisService],
  templateUrl: './view-ai-keyword-analyzer.component.html',
  styleUrl: './view-ai-keyword-analyzer.component.scss'
})
export class ViewAiKeywordAnalyzerComponent implements OnInit {
   id: any;
   keyword!: AIKeywordDto;

  private _modalService = inject(BsModalService);
  constructor(
    injector: Injector,
    protected _cdr: ChangeDetectorRef,
    private apiService: ApisService
  ) {}
  
  ngOnInit() {
    console.log('Received ID in modal:', this.id);
      if (this.id) {
      this.getById(this.id);
    }
  }

  getById(id:any)
  {
     this.apiService.getByIdAIKeyword(id).subscribe({
      next: (response) => {
        this.keyword = response
        console.log('Keyword Details:', this.keyword);
      },
      error: (err) => {
        console.error('Failed to fetch keyword data:', err);
      }
    });
  }
}