.resume-screening-container {
  max-width: 800px;
  margin: 2rem auto;
  padding: 2rem;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  font-family: system-ui, sans-serif;
}

.input-section {
  margin-bottom: 1.5rem;
}

textarea {
  width: 100%;
  height: 140px;
  padding: 0.75rem;
  font-size: 1rem;
  border: 1px solid #ccc;
  border-radius: 8px;
  resize: vertical;
}

input[type='file'] {
  font-size: 1rem;
  margin-top: 0.5rem;
}

label {
  font-weight: 600;
  display: block;
  margin-bottom: 0.5rem;
}

button {
  background-color: #3f51b5;
  color: white;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  margin-top: 1rem;
}

button:hover {
  background-color: #2c3e90;
}

.result {
  margin-top: 2rem;
  background: #f1f9f1;
  padding: 1rem;
  border-radius: 8px;
}
